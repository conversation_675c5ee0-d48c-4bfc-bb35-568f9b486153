#!/bin/bash

# Create Cloudflare API Token using Global API Key
echo "🔑 Create Cloudflare API Token for MCP Integration"
echo "=================================================="
echo ""

# Check dependencies
if ! command -v jq &> /dev/null; then
    echo "❌ jq is required. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install jq
    else
        sudo apt-get install -y jq
    fi
fi

echo "📧 You need your Cloudflare Global API Key for this process."
echo "🔗 Get it from: https://dash.cloudflare.com/profile/api-tokens"
echo "   (Scroll down to 'Global API Key' section and click 'View')"
echo ""

# Get credentials
read -p "Enter your Cloudflare email: " CF_EMAIL
echo ""
echo "🔑 Enter your Global API Key:"
read -s -p "Global API Key: " CF_GLOBAL_KEY
echo ""
echo ""

# Validate credentials first
echo "🧪 Testing credentials..."
TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')

if [ "$TEST_SUCCESS" != "true" ]; then
    echo "❌ Invalid credentials. Please check your email and Global API Key."
    echo "Response: $(echo $TEST_RESPONSE | jq -r '.errors[0].message')"
    exit 1
fi

echo "✅ Credentials validated!"
echo ""

# Create comprehensive API token
echo "🏗️  Creating comprehensive API token for MCP..."

RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY" \
  -H "Content-Type: application/json" \
  --data '{
    "name": "MCP Development Token - '$(date +%Y%m%d_%H%M)'",
    "policies": [
      {
        "effect": "allow",
        "resources": {
          "com.cloudflare.api.account.*": "*"
        },
        "permission_groups": [
          {
            "id": "c8fed203ed3043cba015a93ad1616f1f",
            "name": "Zone:Read"
          },
          {
            "id": "82a06845c3be4d23af2480746407edcb",
            "name": "Zone:Edit"
          },
          {
            "id": "4755a26eedb94da69e1066d98aa820be",
            "name": "DNS:Edit"
          },
          {
            "id": "3030687196ad4229b1958a6a7bf0c4d6",
            "name": "Workers Scripts:Edit"
          },
          {
            "id": "e086da7e2179491d91ee5f35b3ca210a",
            "name": "Workers KV Storage:Edit"
          },
          {
            "id": "f7f0eda5697f475c90846e879bab8666",
            "name": "Account Analytics:Read"
          },
          {
            "id": "05dd6a8c6a6c44af95045cd4ba8df609",
            "name": "User Details:Read"
          }
        ]
      },
      {
        "effect": "allow",
        "resources": {
          "com.cloudflare.api.account.zone.*": "*"
        },
        "permission_groups": [
          {
            "id": "c8fed203ed3043cba015a93ad1616f1f",
            "name": "Zone:Read"
          },
          {
            "id": "82a06845c3be4d23af2480746407edcb",
            "name": "Zone:Edit"
          },
          {
            "id": "4755a26eedb94da69e1066d98aa820be",
            "name": "DNS:Edit"
          },
          {
            "id": "bfb0779a7b8c4bd6bb4c3df5abb3edaf",
            "name": "Zone Settings:Edit"
          },
          {
            "id": "c1fde68c7bcc44588cbb6ddbc16d6480",
            "name": "Zone Analytics:Read"
          },
          {
            "id": "fb0af2e7cfa74e9dbb94d013a3230318",
            "name": "Logs:Read"
          }
        ]
      }
    ]
  }')

# Check if token creation was successful
SUCCESS=$(echo $RESPONSE | jq -r '.success')
TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')

if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
    echo ""
    echo "🎉 SUCCESS! API Token created successfully!"
    echo "=================================================="
    echo ""
    echo "🔑 Your new API Token:"
    echo "$TOKEN"
    echo ""
    
    # Save token to file
    echo "$TOKEN" > cloudflare_api_token.txt
    echo "💾 Token saved to: cloudflare_api_token.txt"
    echo ""
    
    # Test the new token
    echo "🧪 Testing new token..."
    TEST_NEW_TOKEN=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
      -H "Authorization: Bearer $TOKEN")
    
    TEST_NEW_SUCCESS=$(echo $TEST_NEW_TOKEN | jq -r '.success')
    
    if [ "$TEST_NEW_SUCCESS" = "true" ]; then
        echo "✅ New token is working perfectly!"
    else
        echo "⚠️  Token created but verification failed"
    fi
    
    echo ""
    echo "📋 Next Steps:"
    echo "=============="
    echo ""
    echo "1. 🔧 Add to environment variables:"
    echo "   export CLOUDFLARE_API_TOKEN=\"$TOKEN\""
    echo ""
    echo "2. 📝 Configure MCP in Augment Code:"
    echo "   - Press Cmd/Ctrl + Shift + P"
    echo "   - Type 'Edit Settings' and select it"
    echo "   - Go to Advanced → Edit in settings.json"
    echo "   - Add this configuration:"
    echo ""
    
    # Generate MCP configuration
    cat > augment_mcp_config.json << EOF
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-observability",
        "command": "npx",
        "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
EOF

    echo "   📁 Copy content from: augment_mcp_config.json"
    echo ""
    echo "3. 🔄 Restart your editor"
    echo ""
    echo "4. 🧪 Test MCP by asking Augment Code:"
    echo "   'Show me my Cloudflare account information'"
    echo "   'List my Cloudflare zones'"
    echo "   'Help me create a new Worker'"
    echo ""
    echo "✅ MCP configuration saved to: augment_mcp_config.json"
    
else
    echo ""
    echo "❌ ERROR: Failed to create token"
    echo "Response:"
    echo $RESPONSE | jq '.'
    
    ERRORS=$(echo $RESPONSE | jq -r '.errors[]?.message // empty')
    if [ -n "$ERRORS" ]; then
        echo ""
        echo "Error details:"
        echo "$ERRORS"
    fi
    exit 1
fi

echo ""
echo "🔐 Security Reminders:"
echo "====================="
echo "⚠️  Keep your API token secure"
echo "🚫 Don't commit it to version control"
echo "📅 Consider setting an expiration date"
echo "🔍 Monitor usage in Cloudflare dashboard"
echo ""
echo "🎯 You're all set! Happy coding with Cloudflare MCP! 🚀"
