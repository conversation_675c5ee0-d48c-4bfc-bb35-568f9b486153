#!/bin/bash

# Final Cloudflare API Token Creator with Correct Permission IDs
echo "🎯 Final Cloudflare API Token Creator"
echo "===================================="
echo ""

# Check jq
if ! command -v jq &> /dev/null; then
    echo "Installing jq..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install jq
    else
        sudo apt-get install -y jq
    fi
fi

echo "📧 Enter your Cloudflare credentials:"
echo ""

# Get credentials
read -p "Enter your Cloudflare email: " CF_EMAIL
echo ""
echo "🔑 Enter your Global API Key:"
read -s -p "Global API Key: " CF_GLOBAL_KEY
echo ""
echo ""

# Test credentials
echo "🧪 Testing credentials..."
TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')

if [ "$TEST_SUCCESS" != "true" ]; then
    echo "❌ Invalid credentials."
    exit 1
fi

echo "✅ Credentials valid!"
echo ""

# Create token with correct permission IDs
echo "🏗️  Creating comprehensive API token for MCP website development..."

RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY" \
  -H "Content-Type: application/json" \
  --data "{
    \"name\": \"MCP Website Development Token - $(date +%Y%m%d_%H%M)\",
    \"policies\": [
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"e086da7e2179491d91ee5f35b3ca210a\"},
          {\"id\": \"1a71c399035b4950a1bd1466bbe4f420\"},
          {\"id\": \"f7f0eda5697f475c90846e879bab8666\"},
          {\"id\": \"8b47d2786a534c08a1f94ee8f9f599ef\"},
          {\"id\": \"09b2857d1c31407795e75e3fed8617a1\"},
          {\"id\": \"192192df92ee43ac90f2aeeffce67e35\"},
          {\"id\": \"bf7481a1826f439697cb59a20b22293e\"},
          {\"id\": \"b4992e1108244f5d8bfbd5744320c2e1\"},
          {\"id\": \"2efd5506f9c8494dacb1fa10a3e7d5b6\"},
          {\"id\": \"6a018a9f2fc74eb6b293b0c548f38b39\"},
          {\"id\": \"8acbe5bb0d54464ab867149d7f7cf8ac\"},
          {\"id\": \"05880cd1bdc24d8bae0be2136972816b\"}
        ]
      },
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.zone.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"c8fed203ed3043cba015a93ad1616f1f\"},
          {\"id\": \"e6d2666161e84845a636613608cee8d5\"},
          {\"id\": \"4755a26eedb94da69e1066d98aa820be\"},
          {\"id\": \"82e64a83756745bbbb1c9c2701bf816b\"},
          {\"id\": \"3030687196b94b638145a3953da2b699\"},
          {\"id\": \"517b21aee92c4d89936c976ba6e4be55\"},
          {\"id\": \"c4df38be41c247b3b4b7702e76eadae0\"},
          {\"id\": \"0a6cfe8cd3ed445e918579e2fb13087b\"}
        ]
      }
    ]
  }")

# Check result
SUCCESS=$(echo $RESPONSE | jq -r '.success')
TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')

if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
    echo ""
    echo "🎉 SUCCESS! Comprehensive API Token Created!"
    echo "==========================================="
    echo ""
    echo "🔑 Your API Token:"
    echo "$TOKEN"
    echo ""
    
    # Save token
    echo "$TOKEN" > cloudflare_api_token.txt
    echo "💾 Token saved to: cloudflare_api_token.txt"
    
    # Test token
    echo ""
    echo "🧪 Testing new token..."
    TEST_NEW=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
      -H "Authorization: Bearer $TOKEN")
    
    if [ "$(echo $TEST_NEW | jq -r '.success')" = "true" ]; then
        echo "✅ Token verified and working perfectly!"
    else
        echo "⚠️  Token created but verification failed"
    fi
    
    # Create comprehensive MCP config
    cat > augment_mcp_config.json << EOF
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-observability",
        "command": "npx",
        "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
EOF

    echo ""
    echo "📝 Complete MCP configuration created: augment_mcp_config.json"
    echo ""
    echo "🎯 Final Steps:"
    echo "==============="
    echo "1. 📋 Copy the content from augment_mcp_config.json"
    echo "2. 🔧 Open Augment Code settings:"
    echo "   - Press Cmd/Ctrl + Shift + P"
    echo "   - Type 'Edit Settings'"
    echo "   - Go to Advanced → Edit in settings.json"
    echo "   - Paste the configuration"
    echo "3. 🔄 Restart your editor"
    echo "4. 🧪 Test by asking Augment Code:"
    echo "   - 'Show me my Cloudflare account information'"
    echo "   - 'List my Cloudflare zones'"
    echo "   - 'Help me create a new Worker with D1 database'"
    echo "   - 'Create an R2 bucket for file storage'"
    echo ""
    echo "✅ Token Permissions Include:"
    echo "   🔹 Workers Scripts (Read/Write)"
    echo "   🔹 D1 Databases (Read/Write)"
    echo "   🔹 R2 Storage (Read/Write)"
    echo "   🔹 KV Storage (Read/Write)"
    echo "   🔹 DNS Management (Read/Write)"
    echo "   🔹 Zone Management (Read/Write)"
    echo "   🔹 Analytics & Logs (Read)"
    echo ""
    echo "🚀 You're now ready to build websites with Cloudflare MCP!"
    
else
    echo ""
    echo "❌ Failed to create token"
    echo "Response:"
    echo $RESPONSE | jq '.'
    
    ERRORS=$(echo $RESPONSE | jq -r '.errors[]?.message // empty')
    if [ -n "$ERRORS" ]; then
        echo ""
        echo "Error details:"
        echo "$ERRORS"
    fi
fi

echo ""
echo "🔐 Security Reminder:"
echo "===================="
echo "⚠️  Keep your API token secure"
echo "🚫 Don't commit it to version control"
echo "📅 Consider setting an expiration date"
echo "🔍 Monitor usage in Cloudflare dashboard"
