# 🔑 Manual Cloudflare API Token Setup for MCP

Since <PERSON><PERSON><PERSON> is already authenticated, let's create the API token manually through the Cloudflare dashboard.

## Step 1: Create API Token

1. **Go to Cloudflare Dashboard**: https://dash.cloudflare.com/profile/api-tokens

2. **Click "Create Token"**

3. **Select "Custom token"**

4. **Configure Token Permissions**:

### Account Permissions:
```
Account - Cloudflare Workers:Script:Edit
Account - D1:Edit
Account - R2:Edit
Account - Workers KV Storage:Edit
Account - Workers Tail:Read
Account - Account Analytics:Read
Account - Account Settings:Read
```

### Zone Permissions:
```
Zone - Zone:Read
Zone - Zone:Edit
Zone - DNS:Edit
Zone - Zone Settings:Edit
Zone - Zone Analytics:Read
Zone - Logs:Read
```

### User Permissions:
```
User - User Details:Read
User - Memberships:Read
```

5. **Account Resources**: Select "Include - All accounts"

6. **Zone Resources**: Select "Include - All zones"

7. **Client IP Address Filtering**: Leave blank (or add your IP)

8. **TTL**: Set expiration date (recommended: 1 year)

9. **Click "Continue to summary"** then **"Create Token"**

10. **Copy the token** - it will only be shown once!

## Step 2: Test Your Token

Run this command to test your token:

```bash
curl -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

## Step 3: Configure MCP in Augment Code

1. **Open Augment Code Settings**:
   - Press `Cmd/Ctrl + Shift + P`
   - Type "Edit Settings"
   - Select "Edit Settings"
   - Click "Advanced" → "Edit in settings.json"

2. **Add this configuration** (replace YOUR_TOKEN_HERE with your actual token):

```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "YOUR_TOKEN_HERE"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "YOUR_TOKEN_HERE"
        }
      },
      {
        "name": "cloudflare-observability",
        "command": "npx",
        "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "YOUR_TOKEN_HERE"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
```

3. **Save the file** and **restart your editor**

## Step 4: Test MCP Integration

In Augment Code, try asking:
- "Show me my Cloudflare account information"
- "List my Cloudflare zones"
- "Help me create a new Worker"

## Available MCP Servers:

- **cloudflare-bindings**: Workers, D1 databases, R2 storage, KV storage
- **cloudflare-builds**: Deployment management and build status
- **cloudflare-observability**: Logs, analytics, and debugging
- **cloudflare-docs**: Up-to-date Cloudflare documentation

## Useful Wrangler Commands:

Since Wrangler is authenticated, you can also use:

```bash
# Create a new Worker project
wrangler init my-website

# Create a D1 database
wrangler d1 create my-database

# Create an R2 bucket
wrangler r2 bucket create my-bucket

# Deploy your project
wrangler deploy

# View logs
wrangler tail
```

## Security Notes:

- ⚠️ Keep your API token secure
- 🔒 Don't commit it to version control
- 📅 Set an expiration date
- 🔍 Monitor usage in Cloudflare dashboard
- 🚫 Revoke immediately if compromised

## Next Steps:

1. Create the API token using the dashboard
2. Configure MCP in Augment Code
3. Restart your editor
4. Start building websites with database and storage!

Happy coding! 🚀
