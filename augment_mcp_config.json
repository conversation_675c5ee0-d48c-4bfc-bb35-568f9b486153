{"augment.advanced": {"mcpServers": [{"name": "cloudflare-bindings", "command": "npx", "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"], "env": {"CLOUDFLARE_API_TOKEN": "****************************************"}}, {"name": "cloudflare-builds", "command": "npx", "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"], "env": {"CLOUDFLARE_API_TOKEN": "****************************************"}}, {"name": "cloudflare-docs", "command": "npx", "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]}]}}