#!/bin/bash

# Show Available Cloudflare Permissions
echo "📋 Cloudflare API Permissions Explorer"
echo "======================================"
echo ""

# Check jq
if ! command -v jq &> /dev/null; then
    echo "Installing jq..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install jq
    else
        sudo apt-get install -y jq
    fi
fi

echo "📧 Enter your Cloudflare credentials to see available permissions:"
echo ""

# Get credentials
read -p "Enter your Cloudflare email: " CF_EMAIL
echo ""
echo "🔑 Enter your Global API Key:"
read -s -p "Global API Key: " CF_GLOBAL_KEY
echo ""
echo ""

# Test credentials
echo "🧪 Testing credentials..."
TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')

if [ "$TEST_SUCCESS" != "true" ]; then
    echo "❌ Invalid credentials."
    exit 1
fi

echo "✅ Credentials valid!"
echo ""

# Get available permission groups
echo "📋 Getting all available permission groups..."
PERMS_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/permission_groups" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

echo ""
echo "🔍 Available Permission Groups:"
echo "==============================="
echo $PERMS_RESPONSE | jq -r '.result[] | "\(.name) = \(.id)"' | sort

echo ""
echo "🎯 Recommended permissions for MCP website development:"
echo "======================================================"

# Find relevant permissions
ZONE_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("Zone")) | "\(.name) = \(.id)"')
DNS_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("DNS")) | "\(.name) = \(.id)"')
WORKER_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("Worker")) | "\(.name) = \(.id)"')
D1_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("D1")) | "\(.name) = \(.id)"')
R2_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("R2")) | "\(.name) = \(.id)"')
KV_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("KV")) | "\(.name) = \(.id)"')
USER_PERMS=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name | contains("User")) | "\(.name) = \(.id)"')

echo ""
echo "🏢 Zone & DNS Permissions:"
echo "$ZONE_PERMS"
echo "$DNS_PERMS"

echo ""
echo "⚡ Workers Permissions:"
echo "$WORKER_PERMS"

echo ""
echo "🗄️  Database & Storage Permissions:"
echo "$D1_PERMS"
echo "$R2_PERMS"
echo "$KV_PERMS"

echo ""
echo "👤 User Permissions:"
echo "$USER_PERMS"

echo ""
echo "💡 Now you can create a token manually with these exact permission IDs!"
echo ""
echo "🔗 Create token at: https://dash.cloudflare.com/profile/api-tokens"
echo "   Or use the permission IDs above in a custom script."
