#!/bin/bash

# Complete Cloudflare MCP Setup Script
# This script installs dependencies, creates API token, and configures MCP

echo "🚀 Complete Cloudflare MCP Setup for Augment Code"
echo "================================================"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check and install dependencies
echo "📦 Checking dependencies..."

# Check Node.js and npm
if ! command_exists node; then
    echo "❌ Node.js is required but not installed."
    echo "Install from: https://nodejs.org/"
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm is required but not installed."
    exit 1
fi

echo "✅ Node.js and npm found"

# Check/install jq
if ! command_exists jq; then
    echo "⚠️  jq not found. Installing..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command_exists brew; then
            brew install jq
        else
            echo "❌ Please install Homebrew first: https://brew.sh/"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y jq
    else
        echo "❌ Please install jq manually: https://stedolan.github.io/jq/"
        exit 1
    fi
fi

echo "✅ jq found"

# Check/install Wrangler
if ! command_exists wrangler; then
    echo "📦 Installing Wrangler CLI..."
    npm install -g wrangler
fi

echo "✅ Wrangler CLI ready"

echo ""
echo "🔐 Choose authentication method:"
echo "1. Use Wrangler login (recommended - opens browser)"
echo "2. Use Global API Key (manual)"
echo "3. Use existing API token"

read -p "Enter choice (1-3): " AUTH_CHOICE

case $AUTH_CHOICE in
    1)
        echo ""
        echo "🌐 Opening browser for Cloudflare authentication..."
        wrangler login
        
        echo ""
        echo "✅ Authentication complete!"
        echo ""
        echo "🔑 Your Wrangler is now authenticated and ready to use."
        echo "You can now use Wrangler commands directly."
        echo ""
        echo "To get your API token for MCP configuration:"
        echo "1. Go to: https://dash.cloudflare.com/profile/api-tokens"
        echo "2. Create a custom token with these permissions:"
        echo "   - Account: Workers Scripts:Edit, D1:Edit, R2:Edit, KV:Edit"
        echo "   - Zone: Zone:Edit, DNS:Edit, Analytics:Read"
        echo "   - User: User Details:Read"
        echo ""
        ;;
        
    2)
        echo ""
        echo "📧 Enter your Cloudflare email:"
        read -p "Email: " CF_EMAIL
        
        echo ""
        echo "🔑 Enter your Global API Key:"
        echo "   Find it at: https://dash.cloudflare.com/profile/api-tokens"
        read -s -p "Global API Key: " CF_API_KEY
        echo ""
        
        # Create token using the previous script logic
        echo ""
        echo "🏗️  Creating API token..."
        
        RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
          -H "X-Auth-Email: $CF_EMAIL" \
          -H "X-Auth-Key: $CF_API_KEY" \
          -H "Content-Type: application/json" \
          --data '{
            "name": "Website Development MCP Token - '$(date +%Y%m%d_%H%M)'",
            "policies": [
              {
                "effect": "allow",
                "resources": {
                  "com.cloudflare.api.account.*": "*"
                },
                "permission_groups": [
                  {"id": "c8fed203ed3043cba015a93ad1616f1f"},
                  {"id": "82a06845c3be4d23af2480746407edcb"},
                  {"id": "4755a26eedb94da69e1066d98aa820be"},
                  {"id": "3030687196ad4229b1958a6a7bf0c4d6"},
                  {"id": "e086da7e2179491d91ee5f35b3ca210a"},
                  {"id": "f7f0eda5697f475c90846e879bab8666"},
                  {"id": "05dd6a8c6a6c44af95045cd4ba8df609"}
                ]
              },
              {
                "effect": "allow",
                "resources": {
                  "com.cloudflare.api.zone.*": "*"
                },
                "permission_groups": [
                  {"id": "c8fed203ed3043cba015a93ad1616f1f"},
                  {"id": "82a06845c3be4d23af2480746407edcb"},
                  {"id": "4755a26eedb94da69e1066d98aa820be"},
                  {"id": "bfb0779a7b8c4bd6bb4c3df5abb3edaf"},
                  {"id": "c1fde68c7bcc44588cbb6ddbc16d6480"},
                  {"id": "fb0af2e7cfa74e9dbb94d013a3230318"}
                ]
              }
            ]
          }')
        
        SUCCESS=$(echo $RESPONSE | jq -r '.success')
        TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')
        
        if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
            echo ""
            echo "🎉 SUCCESS! API Token created!"
            echo "🔑 Token: $TOKEN"
            echo "$TOKEN" > cloudflare_api_token.txt
            echo "💾 Saved to: cloudflare_api_token.txt"
        else
            echo "❌ Failed to create token"
            echo $RESPONSE | jq '.'
            exit 1
        fi
        ;;
        
    3)
        echo ""
        echo "🔑 Enter your existing API token:"
        read -s -p "API Token: " TOKEN
        echo ""
        echo "$TOKEN" > cloudflare_api_token.txt
        echo "💾 Token saved to: cloudflare_api_token.txt"
        ;;
        
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

# Test the token if we have one
if [ -n "$TOKEN" ]; then
    echo ""
    echo "🧪 Testing API token..."
    
    TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
      -H "Authorization: Bearer $TOKEN")
    
    TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')
    
    if [ "$TEST_SUCCESS" = "true" ]; then
        echo "✅ Token is valid!"
    else
        echo "❌ Token test failed"
        echo $TEST_RESPONSE | jq '.'
    fi
fi

# Generate MCP configuration
echo ""
echo "📝 Generating MCP configuration for Augment Code..."

if [ -f "cloudflare_api_token.txt" ]; then
    SAVED_TOKEN=$(cat cloudflare_api_token.txt)
    
    cat > augment_mcp_config.json << EOF
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$SAVED_TOKEN"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$SAVED_TOKEN"
        }
      },
      {
        "name": "cloudflare-observability",
        "command": "npx",
        "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$SAVED_TOKEN"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
EOF

    echo "✅ MCP configuration saved to: augment_mcp_config.json"
fi

echo ""
echo "🎯 Setup Complete! Next steps:"
echo "================================"
echo ""
echo "1. 📋 Copy the MCP configuration:"
echo "   - Open Augment Code settings (Cmd/Ctrl + Shift + P → Edit Settings)"
echo "   - Go to Advanced → Edit in settings.json"
echo "   - Add the content from: augment_mcp_config.json"
echo ""
echo "2. 🔄 Restart your editor"
echo ""
echo "3. 🧪 Test MCP integration in Augment Code by asking:"
echo "   'Show me my Cloudflare account information'"
echo ""
echo "4. 📚 Available MCP servers:"
echo "   - cloudflare-bindings (Workers, D1, R2, KV)"
echo "   - cloudflare-builds (Deployment management)"
echo "   - cloudflare-observability (Logs and analytics)"
echo "   - cloudflare-docs (Documentation)"
echo ""
echo "🔗 Useful commands:"
echo "   wrangler dev          # Local development"
echo "   wrangler deploy       # Deploy to production"
echo "   wrangler d1 create    # Create database"
echo "   wrangler r2 bucket create # Create storage bucket"
echo ""
echo "Happy coding! 🚀"
