#!/bin/bash

# Get Cloudflare API Token using authenticated Wrangler session
echo "🔑 Getting Cloudflare API Token via Wrangler CLI"
echo "================================================"
echo ""

# Check if wrangler is authenticated
echo "📋 Checking Wrangler authentication..."
if ! wrangler whoami > /dev/null 2>&1; then
    echo "❌ Wrangler is not authenticated. Please run: wrangler login"
    exit 1
fi

echo "✅ Wrangler is authenticated"
echo ""

# Get account info
echo "📊 Getting account information..."
ACCOUNT_INFO=$(wrangler whoami 2>/dev/null)
ACCOUNT_ID=$(echo "$ACCOUNT_INFO" | grep -A 10 "Account ID" | tail -1 | awk '{print $NF}' | tr -d '│ ')
EMAIL=$(echo "$ACCOUNT_INFO" | grep "email" | sed 's/.*email //' | sed 's/\.//')

echo "Account ID: $ACCOUNT_ID"
echo "Email: $EMAIL"
echo ""

# Method 1: Try to extract token from wrangler config
echo "🔍 Method 1: Checking Wrangler config for existing token..."

# Check common wrangler config locations
CONFIG_LOCATIONS=(
    "$HOME/.wrangler/config/default.toml"
    "$HOME/.config/wrangler/config.toml" 
    "$HOME/Library/Application Support/wrangler/config.toml"
    "$HOME/.wrangler/config.toml"
)

TOKEN_FOUND=""
for config_path in "${CONFIG_LOCATIONS[@]}"; do
    if [ -f "$config_path" ]; then
        echo "Found config at: $config_path"
        if grep -q "api_token" "$config_path" 2>/dev/null; then
            TOKEN_FOUND=$(grep "api_token" "$config_path" | cut -d'"' -f2 2>/dev/null)
            if [ -n "$TOKEN_FOUND" ]; then
                echo "✅ Found API token in config!"
                break
            fi
        fi
    fi
done

if [ -z "$TOKEN_FOUND" ]; then
    echo "⚠️  No API token found in Wrangler config (OAuth tokens are not stored as API tokens)"
    echo ""
    
    # Method 2: Create a new API token using Cloudflare API
    echo "🔧 Method 2: Creating new API token via Cloudflare API..."
    echo ""
    
    # Try to use wrangler to make API calls (this might work with OAuth)
    echo "Attempting to create API token using Wrangler's authentication..."
    
    # Create a temporary script to make API calls through wrangler
    cat > temp_api_call.js << 'EOF'
const https = require('https');

// This would need to be adapted to work with Wrangler's OAuth token
// For now, we'll provide instructions for manual creation
console.log("OAuth tokens cannot be directly converted to API tokens via CLI.");
console.log("Please use the manual method or create via dashboard.");
EOF

    node temp_api_call.js
    rm temp_api_call.js
    
    echo ""
    echo "📋 Alternative: Create token manually via CLI commands"
    echo ""
    
    # Method 3: Provide curl commands with instructions
    echo "🌐 Method 3: Manual API token creation"
    echo ""
    echo "Since you're using OAuth authentication, you'll need to create an API token manually."
    echo "Here's the exact curl command you can use once you have a Global API Key:"
    echo ""
    
    cat << 'EOF'
# First, get your Global API Key from: https://dash.cloudflare.com/profile/api-tokens
# Then run this command:

curl -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
  -H "X-Auth-Email: YOUR_EMAIL" \
  -H "X-Auth-Key: YOUR_GLOBAL_API_KEY" \
  -H "Content-Type: application/json" \
  --data '{
    "name": "MCP Development Token",
    "policies": [
      {
        "effect": "allow",
        "resources": {
          "com.cloudflare.api.account.*": "*"
        },
        "permission_groups": [
          {"id": "c8fed203ed3043cba015a93ad1616f1f", "name": "Zone:Read"},
          {"id": "82a06845c3be4d23af2480746407edcb", "name": "Zone:Edit"},
          {"id": "4755a26eedb94da69e1066d98aa820be", "name": "DNS:Edit"},
          {"id": "3030687196ad4229b1958a6a7bf0c4d6", "name": "Workers Scripts:Edit"},
          {"id": "e086da7e2179491d91ee5f35b3ca210a", "name": "Workers KV Storage:Edit"},
          {"id": "f7f0eda5697f475c90846e879bab8666", "name": "Account Analytics:Read"},
          {"id": "05dd6a8c6a6c44af95045cd4ba8df609", "name": "User Details:Read"}
        ]
      },
      {
        "effect": "allow", 
        "resources": {
          "com.cloudflare.api.zone.*": "*"
        },
        "permission_groups": [
          {"id": "c8fed203ed3043cba015a93ad1616f1f", "name": "Zone:Read"},
          {"id": "82a06845c3be4d23af2480746407edcb", "name": "Zone:Edit"},
          {"id": "4755a26eedb94da69e1066d98aa820be", "name": "DNS:Edit"},
          {"id": "bfb0779a7b8c4bd6bb4c3df5abb3edaf", "name": "Zone Settings:Edit"},
          {"id": "c1fde68c7bcc44588cbb6ddbc16d6480", "name": "Zone Analytics:Read"},
          {"id": "fb0af2e7cfa74e9dbb94d013a3230318", "name": "Logs:Read"}
        ]
      }
    ]
  }'
EOF
    
else
    echo "🎉 Found API Token: $TOKEN_FOUND"
    echo ""
    echo "💾 Saving token to file..."
    echo "$TOKEN_FOUND" > cloudflare_api_token.txt
    echo "✅ Token saved to: cloudflare_api_token.txt"
fi

echo ""
echo "🧪 Testing current Wrangler capabilities..."

# Test what we can do with current authentication
echo ""
echo "📊 Available Wrangler commands with current auth:"
echo "wrangler d1 list                    # List D1 databases"
echo "wrangler r2 bucket list             # List R2 buckets" 
echo "wrangler kv:namespace list          # List KV namespaces"
echo "wrangler pages project list         # List Pages projects"
echo ""

# Try to list some resources to verify access
echo "🔍 Testing access to Cloudflare resources..."

echo "D1 Databases:"
wrangler d1 list 2>/dev/null || echo "  No D1 databases found or access denied"

echo ""
echo "R2 Buckets:"
wrangler r2 bucket list 2>/dev/null || echo "  No R2 buckets found or access denied"

echo ""
echo "KV Namespaces:"
wrangler kv:namespace list 2>/dev/null || echo "  No KV namespaces found or access denied"

echo ""
echo "📋 Summary:"
echo "=========="
if [ -n "$TOKEN_FOUND" ]; then
    echo "✅ API Token: Found and saved"
    echo "📁 Location: cloudflare_api_token.txt"
    echo ""
    echo "🔧 Next: Configure MCP in Augment Code with this token"
else
    echo "⚠️  API Token: Not found (OAuth authentication doesn't provide API tokens)"
    echo "📋 Action needed: Create API token manually via dashboard or Global API Key"
    echo ""
    echo "🔗 Dashboard: https://dash.cloudflare.com/profile/api-tokens"
fi

echo "✅ Wrangler CLI: Fully functional for development"
echo ""
echo "🚀 You can start developing with Wrangler commands immediately!"
echo "   For MCP integration, you'll need an API token from the dashboard."
