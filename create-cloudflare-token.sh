#!/bin/bash

# Cloudflare API Token Creator for Website Development
# This script creates a comprehensive API token for MCP integration

echo "🚀 Cloudflare API Token Creator for Website Development"
echo "=================================================="
echo ""

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "❌ jq is required but not installed."
    echo "Install it with:"
    echo "  macOS: brew install jq"
    echo "  Ubuntu/Debian: sudo apt-get install jq"
    echo "  Windows: Download from https://stedolan.github.io/jq/"
    exit 1
fi

# Get user credentials
echo "📧 Enter your Cloudflare credentials:"
read -p "Email: " CF_EMAIL

echo ""
echo "🔑 Enter your Global API Key (found at: https://dash.cloudflare.com/profile/api-tokens)"
echo "   Or press Enter to use an existing API token instead:"
read -s -p "Global API Key (or existing token): " CF_AUTH
echo ""

# Determine auth method
if [[ ${#CF_AUTH} -eq 37 ]] && [[ $CF_AUTH == *"."* ]]; then
    # Looks like an API token
    AUTH_HEADER="Authorization: Bearer $CF_AUTH"
    echo "✅ Using API Token authentication"
else
    # Assume it's a Global API Key
    AUTH_HEADER_EMAIL="X-Auth-Email: $CF_EMAIL"
    AUTH_HEADER_KEY="X-Auth-Key: $CF_AUTH"
    echo "✅ Using Global API Key authentication"
fi

echo ""
echo "🏗️  Creating comprehensive API token for website development..."

# Create the API token with comprehensive permissions
if [[ ${#CF_AUTH} -eq 37 ]] && [[ $CF_AUTH == *"."* ]]; then
    # Using token auth
    RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
      -H "$AUTH_HEADER" \
      -H "Content-Type: application/json" \
      --data '{
        "name": "Website Development MCP Token - '$(date +%Y%m%d_%H%M)'",
        "policies": [
          {
            "effect": "allow",
            "resources": {
              "com.cloudflare.api.account.*": "*"
            },
            "permission_groups": [
              {
                "id": "c8fed203ed3043cba015a93ad1616f1f",
                "name": "Zone:Read"
              },
              {
                "id": "82a06845c3be4d23af2480746407edcb", 
                "name": "Zone:Edit"
              },
              {
                "id": "4755a26eedb94da69e1066d98aa820be",
                "name": "DNS:Edit"
              },
              {
                "id": "3030687196ad4229b1958a6a7bf0c4d6",
                "name": "Workers Scripts:Edit"
              },
              {
                "id": "b68204ab2b8c4c5b8b6c8f1c5b8b6c8f",
                "name": "D1:Edit"
              },
              {
                "id": "a68204ab2b8c4c5b8b6c8f1c5b8b6c8f", 
                "name": "R2:Edit"
              },
              {
                "id": "e086da7e2179491d91ee5f35b3ca210a",
                "name": "Workers KV Storage:Edit"
              },
              {
                "id": "f7f0eda5697f475c90846e879bab8666",
                "name": "Account Analytics:Read"
              },
              {
                "id": "05dd6a8c6a6c44af95045cd4ba8df609",
                "name": "User Details:Read"
              }
            ]
          },
          {
            "effect": "allow",
            "resources": {
              "com.cloudflare.api.zone.*": "*"
            },
            "permission_groups": [
              {
                "id": "c8fed203ed3043cba015a93ad1616f1f",
                "name": "Zone:Read"
              },
              {
                "id": "82a06845c3be4d23af2480746407edcb",
                "name": "Zone:Edit"
              },
              {
                "id": "4755a26eedb94da69e1066d98aa820be",
                "name": "DNS:Edit"
              },
              {
                "id": "bfb0779a7b8c4bd6bb4c3df5abb3edaf",
                "name": "Zone Settings:Edit"
              },
              {
                "id": "c1fde68c7bcc44588cbb6ddbc16d6480",
                "name": "Zone Analytics:Read"
              },
              {
                "id": "fb0af2e7cfa74e9dbb94d013a3230318",
                "name": "Logs:Read"
              }
            ]
          }
        ]
      }')
else
    # Using Global API Key auth
    RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
      -H "$AUTH_HEADER_EMAIL" \
      -H "$AUTH_HEADER_KEY" \
      -H "Content-Type: application/json" \
      --data '{
        "name": "Website Development MCP Token - '$(date +%Y%m%d_%H%M)'",
        "policies": [
          {
            "effect": "allow",
            "resources": {
              "com.cloudflare.api.account.*": "*"
            },
            "permission_groups": [
              {
                "id": "c8fed203ed3043cba015a93ad1616f1f",
                "name": "Zone:Read"
              },
              {
                "id": "82a06845c3be4d23af2480746407edcb",
                "name": "Zone:Edit"
              },
              {
                "id": "4755a26eedb94da69e1066d98aa820be",
                "name": "DNS:Edit"
              },
              {
                "id": "3030687196ad4229b1958a6a7bf0c4d6",
                "name": "Workers Scripts:Edit"
              }
            ]
          },
          {
            "effect": "allow",
            "resources": {
              "com.cloudflare.api.zone.*": "*"
            },
            "permission_groups": [
              {
                "id": "c8fed203ed3043cba015a93ad1616f1f",
                "name": "Zone:Read"
              },
              {
                "id": "82a06845c3be4d23af2480746407edcb",
                "name": "Zone:Edit"
              },
              {
                "id": "4755a26eedb94da69e1066d98aa820be",
                "name": "DNS:Edit"
              }
            ]
          }
        ]
      }')
fi

# Check if the request was successful
SUCCESS=$(echo $RESPONSE | jq -r '.success')
TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')

if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
    echo ""
    echo "🎉 SUCCESS! API Token created successfully!"
    echo "=================================================="
    echo ""
    echo "🔑 Your new API Token:"
    echo "$TOKEN"
    echo ""
    echo "⚠️  IMPORTANT: Save this token securely - it won't be shown again!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Add to your environment variables:"
    echo "   export CLOUDFLARE_API_TOKEN=\"$TOKEN\""
    echo ""
    echo "2. Or save to .env file:"
    echo "   echo 'CLOUDFLARE_API_TOKEN=$TOKEN' >> .env"
    echo ""
    echo "3. Test the token:"
    echo "   curl -X GET \"https://api.cloudflare.com/client/v4/user/tokens/verify\" \\"
    echo "     -H \"Authorization: Bearer $TOKEN\""
    echo ""
    echo "4. Use in Augment Code MCP configuration"
    echo ""
    
    # Save token to file for easy access
    echo "$TOKEN" > cloudflare_api_token.txt
    echo "💾 Token also saved to: cloudflare_api_token.txt"
    
else
    echo ""
    echo "❌ ERROR: Failed to create token"
    echo "Response:"
    echo $RESPONSE | jq '.'
    
    ERRORS=$(echo $RESPONSE | jq -r '.errors[]?.message // empty')
    if [ -n "$ERRORS" ]; then
        echo ""
        echo "Error details:"
        echo "$ERRORS"
    fi
fi

echo ""
echo "🔗 Useful links:"
echo "- Cloudflare Dashboard: https://dash.cloudflare.com"
echo "- API Tokens: https://dash.cloudflare.com/profile/api-tokens"
echo "- MCP Documentation: https://docs.augmentcode.com/setup-augment/mcp"
