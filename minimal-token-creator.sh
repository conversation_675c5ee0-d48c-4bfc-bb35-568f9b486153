#!/bin/bash

# Minimal Cloudflare API Token Creator - Essential Permissions Only
echo "🎯 Minimal Cloudflare API Token for MCP"
echo "======================================="
echo ""

# Check jq
if ! command -v jq &> /dev/null; then
    echo "Installing jq..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install jq
    else
        sudo apt-get install -y jq
    fi
fi

echo "📧 Enter your Cloudflare credentials:"
echo ""

# Get credentials
read -p "Enter your Cloudflare email: " CF_EMAIL
echo ""
echo "🔑 Enter your Global API Key:"
read -s -p "Global API Key: " CF_GLOBAL_KEY
echo ""
echo ""

# Test credentials
echo "🧪 Testing credentials..."
TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')

if [ "$TEST_SUCCESS" != "true" ]; then
    echo "❌ Invalid credentials."
    exit 1
fi

echo "✅ Credentials valid!"
echo ""

# Create minimal token with essential permissions only
echo "🏗️  Creating minimal API token with essential permissions..."

RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY" \
  -H "Content-Type: application/json" \
  --data "{
    \"name\": \"MCP Essential Token - $(date +%Y%m%d_%H%M)\",
    \"policies\": [
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"e086da7e2179491d91ee5f35b3ca210a\"},
          {\"id\": \"f7f0eda5697f475c90846e879bab8666\"},
          {\"id\": \"09b2857d1c31407795e75e3fed8617a1\"},
          {\"id\": \"bf7481a1826f439697cb59a20b22293e\"}
        ]
      },
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.zone.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"c8fed203ed3043cba015a93ad1616f1f\"},
          {\"id\": \"4755a26eedb94da69e1066d98aa820be\"}
        ]
      }
    ]
  }")

# Check result
SUCCESS=$(echo $RESPONSE | jq -r '.success')
TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')

if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
    echo ""
    echo "🎉 SUCCESS! Minimal API Token Created!"
    echo "====================================="
    echo ""
    echo "🔑 Your API Token:"
    echo "$TOKEN"
    echo ""
    
    # Save token
    echo "$TOKEN" > cloudflare_api_token.txt
    echo "💾 Token saved to: cloudflare_api_token.txt"
    
    # Test token
    echo ""
    echo "🧪 Testing new token..."
    TEST_NEW=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
      -H "Authorization: Bearer $TOKEN")
    
    if [ "$(echo $TEST_NEW | jq -r '.success')" = "true" ]; then
        echo "✅ Token verified and working!"
    else
        echo "⚠️  Token created but verification failed"
    fi
    
    # Create MCP config
    cat > augment_mcp_config.json << EOF
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
EOF

    # Also create environment variable setup
    cat > setup_env.sh << EOF
#!/bin/bash
# Add this to your shell profile (.bashrc, .zshrc, etc.)
export CLOUDFLARE_API_TOKEN="$TOKEN"
echo "✅ Cloudflare API token exported to environment"
EOF
    chmod +x setup_env.sh

    echo ""
    echo "📝 MCP configuration created: augment_mcp_config.json"
    echo "🔧 Environment setup script: setup_env.sh"
    echo ""
    echo "🎯 Next Steps:"
    echo "=============="
    echo "1. 📋 Copy content from augment_mcp_config.json"
    echo "2. 🔧 Add to Augment Code settings.json"
    echo "3. 🔄 Restart your editor"
    echo "4. 🧪 Test MCP integration"
    echo ""
    echo "✅ Token includes essential permissions:"
    echo "   🔹 Workers Scripts Write"
    echo "   🔹 Workers KV Storage Write"
    echo "   🔹 D1 Write"
    echo "   🔹 R2 Storage Write"
    echo "   🔹 Zone Read"
    echo "   🔹 DNS Write"
    echo ""
    echo "🚀 Ready for website development with database and storage!"
    
else
    echo ""
    echo "❌ Failed to create token"
    echo "Response:"
    echo $RESPONSE | jq '.'
    
    ERRORS=$(echo $RESPONSE | jq -r '.errors[]?.message // empty')
    if [ -n "$ERRORS" ]; then
        echo ""
        echo "Error details:"
        echo "$ERRORS"
    fi
    
    echo ""
    echo "💡 Alternative: Manual Token Creation"
    echo "===================================="
    echo "If the CLI method fails, create manually at:"
    echo "🔗 https://dash.cloudflare.com/profile/api-tokens"
    echo ""
    echo "Use these permission IDs:"
    echo "Account permissions:"
    echo "- Workers Scripts Write: e086da7e2179491d91ee5f35b3ca210a"
    echo "- Workers KV Storage Write: f7f0eda5697f475c90846e879bab8666"
    echo "- D1 Write: 09b2857d1c31407795e75e3fed8617a1"
    echo "- R2 Storage Write: bf7481a1826f439697cb59a20b22293e"
    echo ""
    echo "Zone permissions:"
    echo "- Zone Read: c8fed203ed3043cba015a93ad1616f1f"
    echo "- DNS Write: 4755a26eedb94da69e1066d98aa820be"
fi
