#!/bin/bash

# Simple Cloudflare API Token Creator
echo "🔑 Simple Cloudflare API Token Creator"
echo "======================================"
echo ""

# Check jq
if ! command -v jq &> /dev/null; then
    echo "Installing jq..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install jq
    else
        sudo apt-get install -y jq
    fi
fi

echo "📧 You need your Cloudflare Global API Key."
echo "🔗 Get it from: https://dash.cloudflare.com/profile/api-tokens"
echo "   (Scroll down to 'Global API Key' and click 'View')"
echo ""

# Get credentials
read -p "Enter your Cloudflare email: " CF_EMAIL
echo ""
echo "🔑 Enter your Global API Key:"
read -s -p "Global API Key: " CF_GLOBAL_KEY
echo ""
echo ""

# Test credentials
echo "🧪 Testing credentials..."
TEST_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

TEST_SUCCESS=$(echo $TEST_RESPONSE | jq -r '.success')

if [ "$TEST_SUCCESS" != "true" ]; then
    echo "❌ Invalid credentials."
    exit 1
fi

echo "✅ Credentials valid!"
echo ""

# Get available permission groups
echo "📋 Getting available permission groups..."
PERMS_RESPONSE=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/permission_groups" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY")

# Extract permission group IDs
ZONE_READ_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="Zone:Read") | .id')
ZONE_EDIT_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="Zone:Edit") | .id')
DNS_EDIT_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="DNS:Edit") | .id')
WORKERS_EDIT_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="Workers Scripts:Edit") | .id')
KV_EDIT_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="Workers KV Storage:Edit") | .id')
USER_READ_ID=$(echo $PERMS_RESPONSE | jq -r '.result[] | select(.name=="User Details:Read") | .id')

echo "Found permission IDs:"
echo "Zone:Read = $ZONE_READ_ID"
echo "Zone:Edit = $ZONE_EDIT_ID"
echo "DNS:Edit = $DNS_EDIT_ID"
echo "Workers Scripts:Edit = $WORKERS_EDIT_ID"
echo "Workers KV Storage:Edit = $KV_EDIT_ID"
echo "User Details:Read = $USER_READ_ID"
echo ""

# Create token with found IDs
echo "🏗️  Creating API token..."

RESPONSE=$(curl -s -X POST "https://api.cloudflare.com/client/v4/user/tokens" \
  -H "X-Auth-Email: $CF_EMAIL" \
  -H "X-Auth-Key: $CF_GLOBAL_KEY" \
  -H "Content-Type: application/json" \
  --data "{
    \"name\": \"MCP Development Token - $(date +%Y%m%d_%H%M)\",
    \"policies\": [
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"$WORKERS_EDIT_ID\"},
          {\"id\": \"$KV_EDIT_ID\"},
          {\"id\": \"$USER_READ_ID\"}
        ]
      },
      {
        \"effect\": \"allow\",
        \"resources\": {
          \"com.cloudflare.api.account.zone.*\": \"*\"
        },
        \"permission_groups\": [
          {\"id\": \"$ZONE_READ_ID\"},
          {\"id\": \"$ZONE_EDIT_ID\"},
          {\"id\": \"$DNS_EDIT_ID\"}
        ]
      }
    ]
  }")

# Check result
SUCCESS=$(echo $RESPONSE | jq -r '.success')
TOKEN=$(echo $RESPONSE | jq -r '.result.value // empty')

if [ "$SUCCESS" = "true" ] && [ -n "$TOKEN" ]; then
    echo ""
    echo "🎉 SUCCESS! API Token created!"
    echo "=============================="
    echo ""
    echo "🔑 Your API Token:"
    echo "$TOKEN"
    echo ""
    
    # Save token
    echo "$TOKEN" > cloudflare_api_token.txt
    echo "💾 Token saved to: cloudflare_api_token.txt"
    
    # Test token
    echo ""
    echo "🧪 Testing new token..."
    TEST_NEW=$(curl -s -X GET "https://api.cloudflare.com/client/v4/user/tokens/verify" \
      -H "Authorization: Bearer $TOKEN")
    
    if [ "$(echo $TEST_NEW | jq -r '.success')" = "true" ]; then
        echo "✅ Token works perfectly!"
    else
        echo "⚠️  Token created but verification failed"
    fi
    
    # Create MCP config
    cat > augment_mcp_config.json << EOF
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "cloudflare-bindings",
        "command": "npx",
        "args": ["mcp-remote", "https://bindings.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-builds",
        "command": "npx",
        "args": ["mcp-remote", "https://builds.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-observability",
        "command": "npx",
        "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"],
        "env": {
          "CLOUDFLARE_API_TOKEN": "$TOKEN"
        }
      },
      {
        "name": "cloudflare-docs",
        "command": "npx",
        "args": ["mcp-remote", "https://docs.mcp.cloudflare.com/sse"]
      }
    ]
  }
}
EOF

    echo ""
    echo "📝 MCP configuration created: augment_mcp_config.json"
    echo ""
    echo "🎯 Next Steps:"
    echo "============="
    echo "1. Copy content from augment_mcp_config.json"
    echo "2. Add to Augment Code settings.json"
    echo "3. Restart your editor"
    echo "4. Test: Ask 'Show me my Cloudflare account'"
    echo ""
    echo "🚀 You're ready to use Cloudflare MCP!"
    
else
    echo ""
    echo "❌ Failed to create token"
    echo "Response: $(echo $RESPONSE | jq '.')"
fi
